/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/track/[orderNumber]";
exports.ids = ["pages/track/[orderNumber]"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Ftrack%2F%5BorderNumber%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Ctrack%5C%5BorderNumber%5D.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Ftrack%2F%5BorderNumber%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Ctrack%5C%5BorderNumber%5D.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_track_orderNumber_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src\\pages\\track\\[orderNumber].tsx */ \"./src/pages/track/[orderNumber].tsx\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_track_orderNumber_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_track_orderNumber_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_track_orderNumber_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_track_orderNumber_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_track_orderNumber_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_track_orderNumber_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_track_orderNumber_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_track_orderNumber_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_track_orderNumber_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_track_orderNumber_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_track_orderNumber_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/track/[orderNumber]\",\n        pathname: \"/track/[orderNumber]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _src_pages_track_orderNumber_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Ftrack%2F%5BorderNumber%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Ctrack%5C%5BorderNumber%5D.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/components/CustomerOrdersModal.tsx":
/*!************************************************!*\
  !*** ./src/components/CustomerOrdersModal.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CustomerOrdersModal)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/api */ \"./src/utils/api.ts\");\n\n\n\nconst ORDER_STATUS_COLORS = {\n    draft: \"bg-gray-100 text-gray-800\",\n    confirmed: \"bg-blue-100 text-blue-800\",\n    processing: \"bg-yellow-100 text-yellow-800\",\n    shipped: \"bg-indigo-100 text-indigo-800\",\n    delivered: \"bg-green-100 text-green-800\",\n    cancelled: \"bg-red-100 text-red-800\",\n    returned: \"bg-orange-100 text-orange-800\"\n};\nconst ORDER_STATUS_LABELS = {\n    draft: \"Draft\",\n    confirmed: \"Confirmed\",\n    processing: \"Processing\",\n    shipped: \"Shipped\",\n    delivered: \"Delivered\",\n    cancelled: \"Cancelled\",\n    returned: \"Returned\"\n};\nfunction CustomerOrdersModal({ isOpen, onClose, customerId, customerName, storeId }) {\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [orders, setOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [total, setTotal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const limit = 10;\n    const totalPages = Math.ceil(total / limit);\n    const fetchOrders = async ()=>{\n        if (!isOpen || !customerId) return;\n        setIsLoading(true);\n        setError(null);\n        try {\n            const result = await _utils_api__WEBPACK_IMPORTED_MODULE_2__.orderApi.filter({\n                customerId: customerId.toString(),\n                page,\n                limit,\n                storeId\n            });\n            if (result && typeof result === \"object\" && \"data\" in result) {\n                setOrders(result.data || []);\n                setTotal(result.meta?.total || 0);\n            } else {\n                setOrders(Array.isArray(result) ? result : []);\n                setTotal(0);\n            }\n        } catch (err) {\n            setError(\"Failed to fetch orders\");\n            console.error(\"Error fetching orders:\", err);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isOpen) {\n            setPage(1);\n            fetchOrders();\n        }\n    }, [\n        isOpen,\n        customerId,\n        page\n    ]);\n    if (!isOpen) return null;\n    const formatCurrency = (amount, currency)=>{\n        try {\n            return new Intl.NumberFormat(\"en\", {\n                style: \"currency\",\n                currency: currency || \"USD\"\n            }).format(amount);\n        } catch  {\n            return `$${amount.toFixed(2)}`;\n        }\n    };\n    const formatDate = (date)=>{\n        return new Date(date).toLocaleDateString();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-2xl font-semibold text-gray-900\",\n                                    children: \"Customer Orders\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this),\n                                customerName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mt-1\",\n                                    children: customerName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-6 h-6\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M6 18L18 6M6 6l12 12\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-6 overflow-y-auto max-h-[calc(90vh-200px)]\",\n                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"ml-3 text-gray-600\",\n                                children: \"Loading orders...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 13\n                    }, this) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-red-500 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-12 h-12 mx-auto\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                                lineNumber: 142,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Failed to load orders. Please try again.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 13\n                    }, this) : orders.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-400 mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-12 h-12 mx-auto\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                                        lineNumber: 153,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"No orders found for this customer.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                                lineNumber: 156,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: orders.map((order)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border border-gray-200 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-gray-900\",\n                                                        children: [\n                                                            \"Order \",\n                                                            order.orderNumber\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: `px-2 py-1 rounded-full text-xs font-medium ${ORDER_STATUS_COLORS[order.status] || \"bg-gray-100 text-gray-800\"}`,\n                                                        children: ORDER_STATUS_LABELS[order.status] || order.status\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                                                        lineNumber: 167,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg font-semibold text-gray-900\",\n                                                        children: formatCurrency(order.total, order.currency)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: formatDate(order.orderDate)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: order.items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between text-sm text-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            item.productName,\n                                                            \" \\xd7 \",\n                                                            item.quantity\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                                                        lineNumber: 186,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: formatCurrency(item.lineTotal, order.currency)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, item.id.toString(), true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 23\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, order.id.toString(), true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                                lineNumber: 161,\n                                columnNumber: 17\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, this),\n                totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between px-6 py-4 border-t border-gray-200 bg-gray-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm text-gray-700\",\n                            children: [\n                                \"Showing \",\n                                (page - 1) * limit + 1,\n                                \" to \",\n                                Math.min(page * limit, total),\n                                \" of \",\n                                total,\n                                \" orders\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setPage(page - 1),\n                                    disabled: page === 1,\n                                    className: \"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: \"Previous\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setPage(page + 1),\n                                    disabled: page === totalPages,\n                                    className: \"px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed\",\n                                    children: \"Next\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                                    lineNumber: 211,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                            lineNumber: 203,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end p-6 border-t border-gray-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: onClose,\n                        className: \"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 transition-colors\",\n                        children: \"Close\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n                    lineNumber: 223,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n            lineNumber: 112,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\components\\\\CustomerOrdersModal.tsx\",\n        lineNumber: 111,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/CustomerOrdersModal.tsx\n");

/***/ }),

/***/ "./src/context/AuthContext.tsx":
/*!*************************************!*\
  !*** ./src/context/AuthContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/auth */ \"./src/utils/auth.ts\");\n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const storageKey = \"teno:auth:user\";\n    const lastUserIdRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const refresh = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            const current = await (0,_utils_auth__WEBPACK_IMPORTED_MODULE_2__.getCurrentUser)();\n            setUser(current);\n            try {\n                if (current) {\n                    localStorage.setItem(storageKey, JSON.stringify(current));\n                } else {\n                    localStorage.removeItem(storageKey);\n                }\n            } catch  {}\n            try {\n                const prevId = lastUserIdRef.current;\n                const nextId = current?.id ?? null;\n                const changed = prevId !== nextId;\n                lastUserIdRef.current = nextId;\n                if (changed && \"undefined\" !== \"undefined\") {}\n            } catch  {}\n        } catch (err) {\n            console.warn(\"[AuthContext] Error during refresh:\", err);\n            setError(\"Failed to load user\");\n            setUser(null);\n            try {\n                localStorage.removeItem(storageKey);\n            } catch  {}\n        } finally{\n            setIsLoading(false);\n        }\n    }, []);\n    const loginWithGoogle = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((nextPath)=>{\n        (0,_utils_auth__WEBPACK_IMPORTED_MODULE_2__.redirectToGoogleAuth)(nextPath);\n    }, []);\n    const logout = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async ()=>{\n        // Optimistically clear local state for snappier UX\n        setUser(null);\n        try {\n            localStorage.removeItem(storageKey);\n        } catch  {}\n        try {\n            await (0,_utils_auth__WEBPACK_IMPORTED_MODULE_2__.performLogout)();\n        } finally{\n            // Always refresh after logout to ensure backend/session is in sync\n            await refresh();\n        }\n    }, [\n        refresh\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Optimistically hydrate from localStorage for fast first paint\n        try {\n            const raw = localStorage.getItem(storageKey);\n            if (raw) {\n                const cached = JSON.parse(raw);\n                setUser(cached);\n                lastUserIdRef.current = cached?.id ?? null;\n                // If we have a cached user, try to refresh from backend\n                refresh();\n            } else {\n                // No cached user, just set loading to false\n                setIsLoading(false);\n            }\n        } catch (e) {\n            console.warn(\"[AuthContext] Error reading cached user:\", e);\n            setIsLoading(false);\n        }\n        // Listen for global unauthorized signals to immediately drop user state\n        const onUnauthorized = ()=>{\n            (0,_utils_auth__WEBPACK_IMPORTED_MODULE_2__.clearClientAuthArtifacts)();\n            setUser(null);\n            lastUserIdRef.current = null;\n        };\n        if (false) {}\n        return ()=>{\n            if (false) {}\n        };\n    }, [\n        refresh\n    ]);\n    const value = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            user,\n            isLoading,\n            error,\n            refresh,\n            loginWithGoogle,\n            logout\n        }), [\n        user,\n        isLoading,\n        error,\n        refresh,\n        loginWithGoogle,\n        logout\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\context\\\\AuthContext.tsx\",\n        lineNumber: 117,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const ctx = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (!ctx) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return ctx;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/context/AuthContext.tsx\n");

/***/ }),

/***/ "./src/context/PreferencesContext.tsx":
/*!********************************************!*\
  !*** ./src/context/PreferencesContext.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PreferencesProvider: () => (/* binding */ PreferencesProvider),\n/* harmony export */   usePreferences: () => (/* binding */ usePreferences)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthContext */ \"./src/context/AuthContext.tsx\");\n/* harmony import */ var _utils_preferences__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/preferences */ \"./src/utils/preferences.ts\");\n/* harmony import */ var _utils_auth__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../utils/auth */ \"./src/utils/auth.ts\");\n\n\n\n\n\nconst PreferencesContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction getBrowserDefaults() {\n    const { language, country, currency } = (0,_utils_preferences__WEBPACK_IMPORTED_MODULE_3__.getBrowserPreferenceDefaults)();\n    return {\n        language,\n        country,\n        currency\n    };\n}\nfunction PreferencesProvider({ children }) {\n    const { user } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const defaults = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>getBrowserDefaults(), []);\n    const [language, setLanguageState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaults.language);\n    const [currency, setCurrencyState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaults.currency);\n    const [country, setCountryState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(defaults.country);\n    const storageKey = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const userId = user?.id ? String(user.id) : \"anon\";\n        const key = `teno:prefs:${userId}`;\n        console.debug(\"[Prefs] storageKey computed\", {\n            userId,\n            key\n        });\n        return key;\n    }, [\n        user?.id\n    ]);\n    // Hydrate from localStorage on mount or when user changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        try {\n            const raw = localStorage.getItem(storageKey);\n            console.debug(\"[Prefs] hydrate start\", {\n                storageKey,\n                raw\n            });\n            if (raw) {\n                const parsed = JSON.parse(raw);\n                const nextLanguage = parsed.language || defaults.language;\n                const nextCurrency = parsed.currency || defaults.currency;\n                const nextCountry = parsed.country || defaults.country;\n                console.debug(\"[Prefs] hydrate parsed\", {\n                    parsed,\n                    nextLanguage,\n                    nextCurrency,\n                    nextCountry\n                });\n                setLanguageState(nextLanguage);\n                setCurrencyState(nextCurrency);\n                setCountryState(nextCountry);\n            } else {\n                console.debug(\"[Prefs] hydrate no existing, using defaults\", defaults);\n                setLanguageState(defaults.language);\n                setCurrencyState(defaults.currency);\n                setCountryState(defaults.country);\n            }\n        } catch  {\n            console.debug(\"[Prefs] hydrate error, falling back to defaults\", defaults);\n            setLanguageState(defaults.language);\n            setCurrencyState(defaults.currency);\n            setCountryState(defaults.country);\n        }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        storageKey\n    ]);\n    // Re-hydrate on auth changes (login/logout) since key scope changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const onAuthChange = ()=>{\n            try {\n                const raw = localStorage.getItem(storageKey);\n                if (raw) {\n                    const parsed = JSON.parse(raw);\n                    setLanguageState(parsed.language || defaults.language);\n                    setCurrencyState(parsed.currency || defaults.currency);\n                    setCountryState(parsed.country || defaults.country);\n                } else {\n                    setLanguageState(defaults.language);\n                    setCurrencyState(defaults.currency);\n                    setCountryState(defaults.country);\n                }\n            } catch  {\n                setLanguageState(defaults.language);\n                setCurrencyState(defaults.currency);\n                setCountryState(defaults.country);\n            }\n        };\n        if (false) {}\n        return ()=>{\n            if (false) {}\n        };\n    }, [\n        storageKey,\n        defaults.language,\n        defaults.currency,\n        defaults.country\n    ]);\n    // If a user is present, fetch server-side preferences and apply\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const userId = user?.id ? String(user.id) : null;\n        if (!userId) return;\n        let aborted = false;\n        (async ()=>{\n            try {\n                const url = `${(0,_utils_auth__WEBPACK_IMPORTED_MODULE_4__.getBackendUrl)()}/users/${encodeURIComponent(userId)}`;\n                console.debug(\"[Prefs] fetching server preferences\", {\n                    userId,\n                    url\n                });\n                const resp = await (0,_utils_auth__WEBPACK_IMPORTED_MODULE_4__.fetchWithCredentials)(url);\n                if (!resp.ok) return;\n                const payload = await resp.json();\n                if (aborted || !payload) return;\n                const nextLanguage = payload.preferredLanguage || defaults.language;\n                const nextCurrency = payload.preferredCurrency || defaults.currency;\n                const nextCountry = payload.countryCode || defaults.country;\n                console.debug(\"[Prefs] server preferences received\", {\n                    nextLanguage,\n                    nextCurrency,\n                    nextCountry\n                });\n                setLanguageState(nextLanguage);\n                setCurrencyState(nextCurrency);\n                setCountryState(nextCountry);\n                try {\n                    localStorage.setItem(storageKey, JSON.stringify({\n                        language: nextLanguage,\n                        currency: nextCurrency,\n                        country: nextCountry\n                    }));\n                } catch  {}\n            } catch  {}\n        })();\n        return ()=>{\n            aborted = true;\n        };\n    }, [\n        user?.id,\n        storageKey,\n        defaults.language,\n        defaults.currency,\n        defaults.country\n    ]);\n    const persist = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((next)=>{\n        try {\n            const current = {\n                language,\n                currency,\n                country\n            };\n            const merged = {\n                ...current,\n                ...next\n            };\n            console.debug(\"[Prefs] persist\", {\n                storageKey,\n                current,\n                next,\n                merged\n            });\n            localStorage.setItem(storageKey, JSON.stringify(merged));\n        } catch  {}\n    }, [\n        language,\n        currency,\n        country,\n        storageKey\n    ]);\n    const setLanguage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((lng)=>{\n        setLanguageState(lng);\n        persist({\n            language: lng\n        });\n    }, [\n        persist\n    ]);\n    const setCurrency = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((cur)=>{\n        console.debug(\"[Prefs] setCurrency\", {\n            from: currency,\n            to: cur\n        });\n        setCurrencyState(cur);\n        persist({\n            currency: cur\n        });\n    }, [\n        persist,\n        currency\n    ]);\n    const setCountry = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((cc)=>{\n        setCountryState(cc);\n        persist({\n            country: cc\n        });\n    }, [\n        persist\n    ]);\n    const value = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            language,\n            currency,\n            country,\n            setLanguage,\n            setCurrency,\n            setCountry\n        }), [\n        language,\n        currency,\n        country,\n        setLanguage,\n        setCurrency,\n        setCountry\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PreferencesContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\context\\\\PreferencesContext.tsx\",\n        lineNumber: 169,\n        columnNumber: 3\n    }, this);\n}\nfunction usePreferences() {\n    const ctx = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(PreferencesContext);\n    if (!ctx) {\n        throw new Error(\"usePreferences must be used within a PreferencesProvider\");\n    }\n    return ctx;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/context/PreferencesContext.tsx\n");

/***/ }),

/***/ "./src/context/StoreContext.tsx":
/*!**************************************!*\
  !*** ./src/context/StoreContext.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   StoreProvider: () => (/* binding */ StoreProvider),\n/* harmony export */   useStore: () => (/* binding */ useStore)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AuthContext */ \"./src/context/AuthContext.tsx\");\n\n\n\nconst StoreContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction StoreProvider({ children }) {\n    const { user } = (0,_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [currentStoreId, setCurrentStoreIdState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const storageKey = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const userId = user?.id ? String(user.id) : \"anon\";\n        return `teno:currentStoreId:${userId}`;\n    }, [\n        user?.id\n    ]);\n    // Hydrate from localStorage on mount or when user changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        try {\n            const raw = localStorage.getItem(storageKey);\n            if (raw) {\n                setCurrentStoreIdState(raw);\n            } else {\n                setCurrentStoreIdState(null);\n            }\n        } catch  {}\n    }, [\n        storageKey\n    ]);\n    // Clear store when user changes (login/logout) because key scope changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const onAuthChange = ()=>{\n            setCurrentStoreIdState(null);\n        };\n        if (false) {}\n        return ()=>{\n            if (false) {}\n        };\n    }, []);\n    const setCurrentStoreId = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((storeId)=>{\n        setCurrentStoreIdState(storeId);\n        try {\n            if (storeId) localStorage.setItem(storageKey, storeId);\n            else localStorage.removeItem(storageKey);\n        } catch  {}\n    }, [\n        storageKey\n    ]);\n    const clearStore = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        setCurrentStoreId(null);\n    }, [\n        setCurrentStoreId\n    ]);\n    const autoSelectFirstStore = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((stores)=>{\n        if (stores.length > 0 && !currentStoreId) {\n            const firstStore = stores[0];\n            setCurrentStoreId(firstStore.id);\n        }\n    }, [\n        currentStoreId,\n        setCurrentStoreId\n    ]);\n    const value = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            currentStoreId,\n            setCurrentStoreId,\n            clearStore,\n            autoSelectFirstStore\n        }), [\n        currentStoreId,\n        setCurrentStoreId,\n        clearStore,\n        autoSelectFirstStore\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StoreContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\context\\\\StoreContext.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\nfunction useStore() {\n    const ctx = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(StoreContext);\n    if (!ctx) {\n        throw new Error(\"useStore must be used within a StoreProvider\");\n    }\n    return ctx;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/context/StoreContext.tsx\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_EntityTable_fadein_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../components/EntityTable.fadein.css */ \"./src/components/EntityTable.fadein.css\");\n/* harmony import */ var _components_EntityTable_fadein_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_components_EntityTable_fadein_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../context/AuthContext */ \"./src/context/AuthContext.tsx\");\n/* harmony import */ var _context_StoreContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../context/StoreContext */ \"./src/context/StoreContext.tsx\");\n/* harmony import */ var _context_PreferencesContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../context/PreferencesContext */ \"./src/context/PreferencesContext.tsx\");\n/* harmony import */ var _utils_useAuthGuard__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../utils/useAuthGuard */ \"./src/utils/useAuthGuard.ts\");\n\n\n\n\n\n\n\nfunction MyApp({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_AuthContext__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_StoreContext__WEBPACK_IMPORTED_MODULE_4__.StoreProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_PreferencesContext__WEBPACK_IMPORTED_MODULE_5__.PreferencesProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GuardedApp, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                        ...pageProps\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 13,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 12,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MyApp);\nfunction GuardedApp({ children }) {\n    // Run global auth guard on every route navigation\n    (0,_utils_useAuthGuard__WEBPACK_IMPORTED_MODULE_6__.useAuthGuard)({\n        publicPaths: [\n            \"/\",\n            \"/login\",\n            \"/_error\",\n            \"/setup/store\",\n            \"/store/[storeUuid]\",\n            \"/storefront\",\n            \"/live/[uuid]\"\n        ]\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: children\n    }, void 0, false);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/track/[orderNumber].tsx":
/*!*******************************************!*\
  !*** ./src/pages/track/[orderNumber].tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TrackOrderPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/head */ \"next/head\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _utils_api__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/api */ \"./src/utils/api.ts\");\n/* harmony import */ var _context_PreferencesContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../context/PreferencesContext */ \"./src/context/PreferencesContext.tsx\");\n/* harmony import */ var _components_CustomerOrdersModal__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/CustomerOrdersModal */ \"./src/components/CustomerOrdersModal.tsx\");\n\n\n\n\n\n\n\nconst baseStatuses = [\n    {\n        value: \"draft\",\n        key: \"draft\"\n    },\n    {\n        value: \"confirmed\",\n        key: \"confirmed\"\n    },\n    {\n        value: \"processing\",\n        key: \"processing\"\n    },\n    {\n        value: \"shipped\",\n        key: \"shipped\"\n    },\n    {\n        value: \"delivered\",\n        key: \"delivered\"\n    },\n    {\n        value: \"cancelled\",\n        key: \"cancelled\"\n    },\n    {\n        value: \"returned\",\n        key: \"returned\"\n    }\n];\nconst messages = {\n    en: {\n        title: \"Track Order - Teno Store\",\n        metaDesc: \"Track your order status\",\n        loading: \"Loading order information...\",\n        enterOrderTitle: \"Enter Order Number\",\n        enterOrderText: \"Please go to the order tracking page and enter your order number.\",\n        goToTracking: \"Go to Order Tracking\",\n        notFoundTitle: \"Order Not Found\",\n        notFoundText: \"The order number you entered could not be found.\",\n        goHome: \"Go Home\",\n        header: \"Order Tracking\",\n        subHeader: \"Track your order status and delivery information\",\n        order: \"Order\",\n        placedOn: \"Placed on\",\n        expectedDelivery: \"Expected Delivery\",\n        orderProgress: \"Order Progress\",\n        orderItems: \"Order Items\",\n        quantity: \"Quantity\",\n        unitPrice: \"Unit Price\",\n        orderSummary: \"Order Summary\",\n        subtotal: \"Subtotal:\",\n        tax: \"Tax:\",\n        total: \"Total:\",\n        backToStore: \"Back to Store\",\n        draft: \"Order Received\",\n        confirmed: \"Order Confirmed\",\n        processing: \"Preparing Order\",\n        shipped: \"Shipped\",\n        delivered: \"Delivered\",\n        cancelled: \"Cancelled\",\n        returned: \"Returned\",\n        draftDesc: \"Your order has been received and is being processed.\",\n        confirmedDesc: \"Your order has been confirmed and is being prepared.\",\n        processingDesc: \"Your order is being prepared for shipment.\",\n        shippedDesc: \"Your order has been shipped and is on its way.\",\n        deliveredDesc: \"Your order has been successfully delivered.\",\n        cancelledDesc: \"Your order has been cancelled.\",\n        returnedDesc: \"Your order has been returned.\",\n        cancellationReasonLabel: \"Cancellation reason\"\n    },\n    fr: {\n        title: \"Suivre la commande - Teno Store\",\n        metaDesc: \"Suivez le statut de votre commande\",\n        loading: \"Chargement des informations de commande...\",\n        enterOrderTitle: \"Saisir le num\\xe9ro de commande\",\n        enterOrderText: \"Veuillez aller \\xe0 la page de suivi et saisir votre num\\xe9ro de commande.\",\n        goToTracking: \"Aller au suivi de commande\",\n        notFoundTitle: \"Commande introuvable\",\n        notFoundText: \"Le num\\xe9ro de commande saisi est introuvable.\",\n        goHome: \"Accueil\",\n        header: \"Suivi de commande\",\n        subHeader: \"Suivez le statut et les informations de livraison\",\n        order: \"Commande\",\n        placedOn: \"Pass\\xe9e le\",\n        expectedDelivery: \"Livraison pr\\xe9vue\",\n        orderProgress: \"Progression de la commande\",\n        orderItems: \"Articles de la commande\",\n        quantity: \"Quantit\\xe9\",\n        unitPrice: \"Prix unitaire\",\n        orderSummary: \"R\\xe9capitulatif de commande\",\n        subtotal: \"Sous-total\\xa0:\",\n        tax: \"Taxe\\xa0:\",\n        total: \"Total\\xa0:\",\n        backToStore: \"Retour \\xe0 la boutique\",\n        draft: \"Commande re\\xe7ue\",\n        confirmed: \"Commande confirm\\xe9e\",\n        processing: \"Pr\\xe9paration de la commande\",\n        shipped: \"Exp\\xe9di\\xe9e\",\n        delivered: \"Livr\\xe9e\",\n        cancelled: \"Annul\\xe9e\",\n        returned: \"Retourn\\xe9e\",\n        draftDesc: \"Votre commande a \\xe9t\\xe9 re\\xe7ue et est en cours de traitement.\",\n        confirmedDesc: \"Votre commande a \\xe9t\\xe9 confirm\\xe9e et est en pr\\xe9paration.\",\n        processingDesc: \"Votre commande est en pr\\xe9paration pour l’exp\\xe9dition.\",\n        shippedDesc: \"Votre commande a \\xe9t\\xe9 exp\\xe9di\\xe9e et est en route.\",\n        deliveredDesc: \"Votre commande a \\xe9t\\xe9 livr\\xe9e avec succ\\xe8s.\",\n        cancelledDesc: \"Votre commande a \\xe9t\\xe9 annul\\xe9e.\",\n        returnedDesc: \"Votre commande a \\xe9t\\xe9 retourn\\xe9e.\",\n        cancellationReasonLabel: \"Raison de l'annulation\"\n    },\n    ar: {\n        title: \"تتبع الطلب - متجر تينو\",\n        metaDesc: \"تتبع حالة طلبك\",\n        loading: \"جارٍ تحميل معلومات الطلب...\",\n        enterOrderTitle: \"أدخل رقم الطلب\",\n        enterOrderText: \"يرجى الذهاب إلى صفحة تتبع الطلب وإدخال رقم الطلب.\",\n        goToTracking: \"الذهاب إلى تتبع الطلب\",\n        notFoundTitle: \"الطلب غير موجود\",\n        notFoundText: \"رقم الطلب الذي أدخلته غير موجود.\",\n        goHome: \"الصفحة الرئيسية\",\n        header: \"تتبع الطلب\",\n        subHeader: \"تتبع حالة الطلب ومعلومات التسليم\",\n        order: \"الطلب\",\n        placedOn: \"تم الطلب في\",\n        expectedDelivery: \"موعد التسليم المتوقع\",\n        orderProgress: \"تقدم الطلب\",\n        orderItems: \"عناصر الطلب\",\n        quantity: \"الكمية\",\n        unitPrice: \"سعر الوحدة\",\n        orderSummary: \"ملخص الطلب\",\n        subtotal: \"المجموع الفرعي:\",\n        tax: \"الضريبة:\",\n        total: \"الإجمالي:\",\n        backToStore: \"العودة إلى المتجر\",\n        draft: \"تم استلام الطلب\",\n        confirmed: \"تم تأكيد الطلب\",\n        processing: \"جاري تجهيز الطلب\",\n        shipped: \"تم الشحن\",\n        delivered: \"تم التسليم\",\n        cancelled: \"تم الإلغاء\",\n        returned: \"تم الإرجاع\",\n        draftDesc: \"تم استلام طلبك وهو قيد المعالجة.\",\n        confirmedDesc: \"تم تأكيد طلبك وهو قيد التحضير.\",\n        processingDesc: \"يتم تجهيز طلبك للشحن.\",\n        shippedDesc: \"تم شحن طلبك وهو في الطريق.\",\n        deliveredDesc: \"تم تسليم طلبك بنجاح.\",\n        cancelledDesc: \"تم إلغاء طلبك.\",\n        returnedDesc: \"تم إرجاع طلبك.\",\n        cancellationReasonLabel: \"سبب الإلغاء\"\n    }\n};\nconst ORDER_STATUSES_FOR = (t)=>[\n        {\n            value: \"draft\",\n            label: t.draft,\n            color: \"text-slate-300\",\n            description: t.draftDesc\n        },\n        {\n            value: \"confirmed\",\n            label: t.confirmed,\n            color: \"text-emerald-300\",\n            description: t.confirmedDesc\n        },\n        {\n            value: \"processing\",\n            label: t.processing,\n            color: \"text-cyan-300\",\n            description: t.processingDesc\n        },\n        {\n            value: \"shipped\",\n            label: t.shipped,\n            color: \"text-indigo-300\",\n            description: t.shippedDesc\n        },\n        {\n            value: \"delivered\",\n            label: t.delivered,\n            color: \"text-emerald-300\",\n            description: t.deliveredDesc\n        },\n        {\n            value: \"cancelled\",\n            label: t.cancelled,\n            color: \"text-red-300\",\n            description: t.cancelledDesc\n        },\n        {\n            value: \"returned\",\n            label: t.returned,\n            color: \"text-orange-300\",\n            description: t.returnedDesc\n        }\n    ];\nconst getStatusStep = (status, statuses)=>{\n    const statusIndex = statuses.findIndex((s)=>s.value === status);\n    return statusIndex >= 0 ? statusIndex : 0;\n};\nconst isCompleted = (currentStep, step)=>{\n    return step <= currentStep;\n};\nconst isCurrent = (currentStep, step)=>{\n    return step === currentStep;\n};\nfunction TrackOrderPage() {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { orderNumber } = router.query;\n    const { language, currency, setLanguage } = (0,_context_PreferencesContext__WEBPACK_IMPORTED_MODULE_5__.usePreferences)();\n    const [order, setOrder] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showCustomerOrders, setShowCustomerOrders] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Normalize order number and check if ready\n    const normalizedOrderNumber = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (typeof orderNumber === \"string\") {\n            return orderNumber.trim().toUpperCase();\n        }\n        return null;\n    }, [\n        orderNumber\n    ]);\n    const isReady = router.isReady;\n    // Extract order currency early for currency formatter\n    const orderCurrency = order?.currency || \"USD\";\n    const langKey = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const lng = (language || \"en\").toLowerCase();\n        if (lng.startsWith(\"fr\")) return \"fr\";\n        if (lng.startsWith(\"ar\")) return \"ar\";\n        return \"en\";\n    }, [\n        language\n    ]);\n    const t = messages[langKey];\n    const dir = langKey === \"ar\" ? \"rtl\" : \"ltr\";\n    const ORDER_STATUSES = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>ORDER_STATUSES_FOR(t), [\n        t\n    ]);\n    const currencyFormatter = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        try {\n            return new Intl.NumberFormat(language || \"en\", {\n                style: \"currency\",\n                currency: orderCurrency\n            });\n        } catch  {\n            return new Intl.NumberFormat(\"en\", {\n                style: \"currency\",\n                currency: \"USD\"\n            });\n        }\n    }, [\n        language,\n        orderCurrency\n    ]);\n    // Sync language from query parameter (?lang=en|fr|ar) and persist via preferences\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!router.isReady) return;\n        const qlang = router.query.lang;\n        if (typeof qlang !== \"string\") return;\n        const normalized = qlang.toLowerCase();\n        const map = {\n            en: \"en-US\",\n            fr: \"fr-FR\",\n            ar: \"ar\"\n        };\n        const next = map[normalized] || qlang;\n        if (next && next !== language) {\n            setLanguage(next);\n        }\n    }, [\n        router.isReady,\n        router.query.lang,\n        setLanguage,\n        language\n    ]);\n    // Get order by order number\n    const fetchOrder = async ()=>{\n        if (!normalizedOrderNumber) return;\n        setIsLoading(true);\n        setError(null);\n        try {\n            const result = await _utils_api__WEBPACK_IMPORTED_MODULE_4__.orderApi.getByOrderNumber(normalizedOrderNumber);\n            if (result && typeof result === \"object\" && \"data\" in result) {\n                setOrder(result.data || result);\n            } else {\n                setOrder(result);\n            }\n        } catch (err) {\n            setError(\"Failed to load order information\");\n            console.error(\"Error fetching order:\", err);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Fetch order when dependencies change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isReady && normalizedOrderNumber) {\n            fetchOrder();\n        }\n    }, [\n        isReady,\n        normalizedOrderNumber\n    ]);\n    // If the router isn't ready yet, keep showing loading state\n    if (!isReady) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            dir: dir,\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: t.title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                            lineNumber: 261,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: t.metaDesc\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                    lineNumber: 260,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                            lineNumber: 265,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: t.loading\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                            lineNumber: 266,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                    lineNumber: 264,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n            lineNumber: 259,\n            columnNumber: 7\n        }, this);\n    }\n    // Graceful state when route is ready but no order number provided\n    if (isReady && !normalizedOrderNumber) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            dir: dir,\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: t.title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: t.metaDesc\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                    lineNumber: 276,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-md w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-lg p-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mx-auto flex h-12 w-12 items-center justify-center rounded-lg bg-indigo-100 mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"h-7 w-7 text-indigo-600\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                lineNumber: 282,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold text-gray-900 mb-2\",\n                                children: t.enterOrderTitle\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                lineNumber: 287,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: t.enterOrderText\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                lineNumber: 288,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push(\"/track\"),\n                                className: \"bg-indigo-600 text-white px-6 py-2 rounded-md hover:bg-indigo-700 transition-colors\",\n                                children: t.goToTracking\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                lineNumber: 289,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                        lineNumber: 281,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                    lineNumber: 280,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n            lineNumber: 275,\n            columnNumber: 7\n        }, this);\n    }\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            dir: dir,\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: t.title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: t.metaDesc\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                            lineNumber: 306,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                    lineNumber: 304,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                            lineNumber: 309,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: t.loading\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                            lineNumber: 310,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                    lineNumber: 308,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n            lineNumber: 303,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !order) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            dir: dir,\n            className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                            children: [\n                                t.notFoundTitle,\n                                \" - Teno Store\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                            lineNumber: 320,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                            name: \"description\",\n                            content: t.metaDesc\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                    lineNumber: 319,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-md w-full\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-lg p-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mx-auto flex h-12 w-12 items-center justify-center rounded-lg bg-red-100 mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"h-7 w-7 text-red-600\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                lineNumber: 325,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold text-gray-900 mb-2\",\n                                children: t.notFoundTitle\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                lineNumber: 330,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mb-6\",\n                                children: error || t.notFoundText\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                lineNumber: 331,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>router.push(\"/\"),\n                                className: \"bg-indigo-600 text-white px-6 py-2 rounded-md hover:bg-indigo-700 transition-colors\",\n                                children: t.goHome\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                        lineNumber: 324,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                    lineNumber: 323,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n            lineNumber: 318,\n            columnNumber: 7\n        }, this);\n    }\n    const currentStatusStep = getStatusStep(order.status, ORDER_STATUSES);\n    const currentStatus = ORDER_STATUSES[currentStatusStep];\n    const orderDate = new Date(order.orderDate);\n    const expectedDeliveryDate = order.expectedDeliveryDate ? new Date(order.expectedDeliveryDate) : null;\n    const subtotal = parseFloat(order.subtotal?.toString?.() ?? \"0\") || 0;\n    const taxAmount = parseFloat(order.taxAmount?.toString?.() ?? \"0\") || 0;\n    const total = parseFloat(order.total?.toString?.() ?? \"0\") || 0;\n    // Filter statuses for display (exclude cancelled/returned unless current status)\n    const displayStatuses = ORDER_STATUSES.filter((status)=>{\n        if (order.status === \"cancelled\" || order.status === \"returned\") {\n            return status.value === order.status || [\n                \"draft\",\n                \"confirmed\"\n            ].includes(status.value);\n        }\n        return ![\n            \"cancelled\",\n            \"returned\"\n        ].includes(status.value);\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        dir: dir,\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_2___default()), {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                        children: t.title\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                        lineNumber: 363,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                        name: \"description\",\n                        content: t.metaDesc\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                        lineNumber: 364,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                lineNumber: 362,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto py-12 px-4 sm:px-6 lg:px-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mx-auto flex h-16 w-16 items-center justify-center rounded-lg bg-indigo-100 mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"h-10 w-10 text-indigo-600\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                lineNumber: 370,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-extrabold text-gray-900 mb-2\",\n                                children: t.header\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                lineNumber: 375,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg text-gray-600\",\n                                children: t.subHeader\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                lineNumber: 376,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                        lineNumber: 369,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-end mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            \"aria-label\": \"Language\",\n                            value: langKey,\n                            onChange: (e)=>{\n                                const val = e.target.value;\n                                const map = {\n                                    en: \"en-US\",\n                                    fr: \"fr-FR\",\n                                    ar: \"ar\"\n                                };\n                                setLanguage(map[val]);\n                            },\n                            className: \"border border-gray-300 rounded-md px-3 py-2 text-sm bg-white focus:outline-none focus:ring-2 focus:ring-indigo-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"en\",\n                                    children: \"English\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"fr\",\n                                    children: \"Fran\\xe7ais\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"ar\",\n                                    children: \"العربية\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                            lineNumber: 381,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                        lineNumber: 380,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-lg p-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col md:flex-row md:items-center justify-between mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-semibold text-gray-900\",\n                                                children: [\n                                                    t.order,\n                                                    \" \",\n                                                    order.orderNumber\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: [\n                                                    t.placedOn,\n                                                    \" \",\n                                                    orderDate.toLocaleDateString()\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-4 md:mt-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: `inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-white border ${currentStatus.color === \"text-emerald-300\" ? \"border-emerald-200 text-emerald-800 bg-emerald-50\" : currentStatus.color === \"text-cyan-300\" ? \"border-cyan-200 text-cyan-800 bg-cyan-50\" : currentStatus.color === \"text-indigo-300\" ? \"border-indigo-200 text-indigo-800 bg-indigo-50\" : currentStatus.color === \"text-yellow-300\" ? \"border-yellow-200 text-yellow-800 bg-yellow-50\" : currentStatus.color === \"text-red-300\" ? \"border-red-200 text-red-800 bg-red-50\" : currentStatus.color === \"text-orange-300\" ? \"border-orange-200 text-orange-800 bg-orange-50\" : \"border-gray-200 text-gray-800 bg-gray-50\"}`,\n                                            children: currentStatus.label\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                            lineNumber: 405,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                        lineNumber: 404,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                lineNumber: 399,\n                                columnNumber: 11\n                            }, this),\n                            order.customer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowCustomerOrders(true),\n                                    className: \"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4 mr-2\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                                lineNumber: 427,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"View Customer Orders\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                lineNumber: 421,\n                                columnNumber: 13\n                            }, this),\n                            expectedDeliveryDate && order.status !== \"cancelled\" && order.status !== \"returned\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-md p-4 mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-5 w-5 text-blue-400 mr-3 mt-0.5\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-blue-800\",\n                                                    children: t.expectedDelivery\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-700\",\n                                                    children: expectedDeliveryDate.toLocaleDateString()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                            lineNumber: 440,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                lineNumber: 435,\n                                columnNumber: 13\n                            }, this),\n                            order.status === \"cancelled\" && order.cancellationReason && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 border border-red-200 rounded-md p-4 mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"h-5 w-5 text-red-400 mr-3 mt-0.5\",\n                                            fill: \"currentColor\",\n                                            viewBox: \"0 0 20 20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                fillRule: \"evenodd\",\n                                                d: \"M8.257 3.099c.366-.446 1.12-.446 1.486 0l6.518 7.933c.46.56.052 1.418-.743 1.418H2.482c-.795 0-1.203-.858-.743-1.418l6.518-7.933zM11 13a1 1 0 10-2 0 1 1 0 002 0zm-1-2a1 1 0 01-1-1V7a1 1 0 112 0v3a1 1 0 01-1 1z\",\n                                                clipRule: \"evenodd\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-sm font-medium text-red-800\",\n                                                    children: t.cancellationReasonLabel\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-red-700\",\n                                                    children: order.cancellationReason\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                                    lineNumber: 456,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                            lineNumber: 454,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                    lineNumber: 450,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                lineNumber: 449,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-medium text-gray-900 mb-6\",\n                                        children: t.orderProgress\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: displayStatuses.map((status, index)=>{\n                                            const stepCompleted = isCompleted(currentStatusStep, ORDER_STATUSES.findIndex((s)=>s.value === status.value));\n                                            const stepCurrent = isCurrent(currentStatusStep, ORDER_STATUSES.findIndex((s)=>s.value === status.value));\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative flex items-start mb-8 last:mb-0\",\n                                                children: [\n                                                    index < displayStatuses.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `absolute left-6 top-12 w-0.5 h-8 ${stepCompleted ? \"bg-emerald-500\" : \"bg-gray-200\"}`\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                                        lineNumber: 474,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `flex items-center justify-center w-12 h-12 rounded-full border-2 ${stepCompleted ? \"bg-emerald-500 border-emerald-500\" : stepCurrent ? \"bg-indigo-500 border-indigo-500\" : \"bg-white border-gray-300\"}`,\n                                                        children: stepCompleted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            className: \"h-6 w-6 text-white\",\n                                                            fill: \"none\",\n                                                            stroke: \"currentColor\",\n                                                            viewBox: \"0 0 24 24\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                strokeLinecap: \"round\",\n                                                                strokeLinejoin: \"round\",\n                                                                strokeWidth: 2,\n                                                                d: \"M5 13l4 4L19 7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                                                lineNumber: 489,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                                            lineNumber: 488,\n                                                            columnNumber: 25\n                                                        }, this) : stepCurrent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 bg-white rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                                            lineNumber: 492,\n                                                            columnNumber: 25\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-3 h-3 bg-gray-300 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                                            lineNumber: 494,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-4 min-w-0 flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: `text-sm font-medium ${stepCompleted || stepCurrent ? \"text-gray-900\" : \"text-gray-500\"}`,\n                                                                children: status.label\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                                                lineNumber: 500,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: `text-sm ${stepCompleted || stepCurrent ? \"text-gray-600\" : \"text-gray-400\"}`,\n                                                                children: status.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                                                lineNumber: 505,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                                        lineNumber: 499,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, status.value, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                lineNumber: 463,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                        lineNumber: 398,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-lg p-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: t.orderItems\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                lineNumber: 520,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: order.items?.map((item)=>{\n                                    const unitPrice = parseFloat(item.unitPrice?.toString?.() ?? \"0\") || 0;\n                                    const lineTotal = parseFloat(item.lineTotal?.toString?.() ?? \"0\") || 0;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between py-4 border-b border-gray-200 last:border-b-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-gray-900\",\n                                                        children: item.productName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                                        lineNumber: 529,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            t.quantity,\n                                                            \": \",\n                                                            item.quantity\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                                        lineNumber: 530,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            t.unitPrice,\n                                                            \": \",\n                                                            currencyFormatter.format(unitPrice)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                                        lineNumber: 531,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-right\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-900\",\n                                                    children: currencyFormatter.format(lineTotal)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, item.id.toString(), true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                        lineNumber: 527,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                lineNumber: 521,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                        lineNumber: 519,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white rounded-lg shadow-lg p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-medium text-gray-900 mb-4\",\n                                children: t.orderSummary\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                lineNumber: 544,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: t.subtotal\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                                lineNumber: 547,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-900\",\n                                                children: currencyFormatter.format(subtotal)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                                lineNumber: 548,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                        lineNumber: 546,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: t.tax\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                                lineNumber: 551,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-900\",\n                                                children: currencyFormatter.format(taxAmount)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                                lineNumber: 552,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                        lineNumber: 550,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-t border-gray-200 pt-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between text-base font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-900\",\n                                                    children: t.total\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                                    lineNumber: 556,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-900\",\n                                                    children: currencyFormatter.format(total)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                                    lineNumber: 557,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                            lineNumber: 555,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                        lineNumber: 554,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                                lineNumber: 545,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                        lineNumber: 543,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                lineNumber: 367,\n                columnNumber: 7\n            }, this),\n            order?.customer && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_CustomerOrdersModal__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                isOpen: showCustomerOrders,\n                onClose: ()=>setShowCustomerOrders(false),\n                customerId: order.customer.id,\n                customerName: order.customer.name || order.customer.email || order.customer.phone || \"Customer\",\n                storeId: order.storeId\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n                lineNumber: 568,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\workspace\\\\projects\\\\teno-store\\\\frontend\\\\src\\\\pages\\\\track\\\\[orderNumber].tsx\",\n        lineNumber: 361,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/track/[orderNumber].tsx\n");

/***/ }),

/***/ "./src/utils/api.ts":
/*!**************************!*\
  !*** ./src/utils/api.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiClient: () => (/* binding */ ApiClient),\n/* harmony export */   agentApi: () => (/* binding */ agentApi),\n/* harmony export */   apiClient: () => (/* binding */ apiClient),\n/* harmony export */   conversationApi: () => (/* binding */ conversationApi),\n/* harmony export */   customerApi: () => (/* binding */ customerApi),\n/* harmony export */   imageApi: () => (/* binding */ imageApi),\n/* harmony export */   orderApi: () => (/* binding */ orderApi),\n/* harmony export */   productApi: () => (/* binding */ productApi),\n/* harmony export */   storeApi: () => (/* binding */ storeApi),\n/* harmony export */   userApi: () => (/* binding */ userApi)\n/* harmony export */ });\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./auth */ \"./src/utils/auth.ts\");\n\nconst API_BASE_URL = \"http://localhost:8000\" || 0;\n// Helper function to handle API responses\nasync function handleResponse(response) {\n    if (!response.ok) {\n        const errorData = await response.json().catch(()=>({}));\n        throw new Error(errorData.error || `HTTP error! status: ${response.status}`);\n    }\n    return response.json();\n}\n// Generic API client class\nclass ApiClient {\n    constructor(baseUrl = API_BASE_URL){\n        this.baseUrl = baseUrl;\n    }\n    // Generic GET request\n    async get(endpoint, params) {\n        const url = new URL(`${this.baseUrl}${endpoint}`);\n        if (params) {\n            Object.entries(params).forEach(([key, value])=>{\n                if (value !== undefined && value !== null) {\n                    url.searchParams.append(key, String(value));\n                }\n            });\n        }\n        console.log(\"API GET request to:\", url.toString());\n        const response = await (0,_auth__WEBPACK_IMPORTED_MODULE_0__.fetchWithCredentials)(url.toString(), {\n            method: \"GET\"\n        });\n        return handleResponse(response);\n    }\n    // Generic POST request\n    async post(endpoint, data) {\n        const url = `${this.baseUrl}${endpoint}`;\n        console.log(\"\\uD83D\\uDD27 API POST request to:\", url);\n        console.log(\"\\uD83D\\uDD27 API POST data:\", data);\n        console.log(\"\\uD83D\\uDD27 API base URL:\", this.baseUrl);\n        try {\n            const response = await (0,_auth__WEBPACK_IMPORTED_MODULE_0__.fetchWithCredentials)(url, {\n                method: \"POST\",\n                body: data ? JSON.stringify(data) : undefined\n            });\n            console.log(\"\\uD83D\\uDD27 API POST response status:\", response.status);\n            console.log(\"\\uD83D\\uDD27 API POST response headers:\", Object.fromEntries(response.headers.entries()));\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error(\"❌ API POST error response:\", errorText);\n                throw new Error(`HTTP error! status: ${response.status}, body: ${errorText}`);\n            }\n            return handleResponse(response);\n        } catch (error) {\n            console.error(\"❌ API POST fetch error:\", error);\n            throw error;\n        }\n    }\n    // Generic PUT request\n    async put(endpoint, data) {\n        const response = await (0,_auth__WEBPACK_IMPORTED_MODULE_0__.fetchWithCredentials)(`${this.baseUrl}${endpoint}`, {\n            method: \"PUT\",\n            body: data ? JSON.stringify(data) : undefined\n        });\n        return handleResponse(response);\n    }\n    // Generic DELETE request\n    async delete(endpoint) {\n        const response = await (0,_auth__WEBPACK_IMPORTED_MODULE_0__.fetchWithCredentials)(`${this.baseUrl}${endpoint}`, {\n            method: \"DELETE\"\n        });\n        return handleResponse(response);\n    }\n}\n// Create a default API client instance\nconst apiClient = new ApiClient();\n// Store API methods\nconst storeApi = {\n    getAll: (params)=>apiClient.get(\"/stores\", params),\n    getById: (id)=>apiClient.get(`/stores/${id}`),\n    getByUserId: (userId, params)=>apiClient.get(`/stores/user/${userId}`, params),\n    getByUuid: (uuid)=>apiClient.get(`/stores/uuid/${uuid}`),\n    create: (data)=>apiClient.post(\"/stores\", data),\n    update: (id, data)=>apiClient.put(`/stores/${id}`, data),\n    delete: (id)=>apiClient.delete(`/stores/${id}`)\n};\n// Product API methods\nconst productApi = {\n    getAll: (params)=>apiClient.get(\"/products\", params),\n    getById: (id)=>apiClient.get(`/products/${id}`),\n    getByStoreId: (storeId, params)=>apiClient.get(`/products/store/${storeId}`, params),\n    getByStoreUuid: (storeUuid, params)=>apiClient.get(`/products/store/uuid/${storeUuid}`, params),\n    create: (data)=>apiClient.post(\"/products\", data),\n    update: (id, data)=>apiClient.put(`/products/${id}`, data),\n    delete: (id)=>apiClient.delete(`/products/${id}`)\n};\n// Customer API methods\nconst customerApi = {\n    getAll: (params)=>apiClient.get(\"/customers\", params),\n    getById: (id)=>apiClient.get(`/customers/${id}`),\n    getByStoreId: (storeId, params)=>apiClient.get(`/customers/store/${storeId}`, params),\n    create: (data)=>apiClient.post(\"/customers\", data),\n    update: (id, data)=>apiClient.put(`/customers/${id}`, data),\n    delete: (id)=>apiClient.delete(`/customers/${id}`)\n};\n// User API methods\nconst userApi = {\n    getAll: (params)=>apiClient.get(\"/users\", params),\n    getById: (id)=>apiClient.get(`/users/${id}`),\n    create: (data)=>apiClient.post(\"/users\", data),\n    update: (id, data)=>apiClient.put(`/users/${id}`, data),\n    delete: (id)=>apiClient.delete(`/users/${id}`)\n};\n// Order API methods\nconst orderApi = {\n    getAll: (params)=>apiClient.get(\"/orders\", params),\n    getById: (id)=>apiClient.get(`/orders/${id}`),\n    getByOrderNumber: (orderNumber)=>apiClient.get(`/orders/order-number/${orderNumber}`),\n    getByStoreId: (storeId, params)=>apiClient.get(`/orders/store/${storeId}`, params),\n    filter: (data)=>apiClient.post(\"/orders/filter\", data),\n    create: (data)=>apiClient.post(\"/orders\", data),\n    update: (id, data)=>apiClient.put(`/orders/${id}`, data),\n    delete: (id)=>apiClient.delete(`/orders/${id}`)\n};\n// Conversation API methods (using existing endpoints)\nconst conversationApi = {\n    getAll: (params)=>apiClient.get(\"/conversations\", params),\n    getById: (id)=>apiClient.get(`/conversations/${id}`),\n    getByUuid: (uuid)=>apiClient.get(`/conversations/uuid/${uuid}`),\n    getByStoreId: (storeId, params)=>apiClient.get(`/conversations/store/${storeId}`, params),\n    getByUserId: (userId, params)=>apiClient.get(`/conversations/user/${userId}`, params),\n    create: (data)=>apiClient.post(\"/conversations\", data),\n    getTimeline: (id, params)=>apiClient.get(`/conversations/${id}/timeline`, params),\n    getUnifiedTimeline: (id, params)=>apiClient.get(`/conversations/${id}/unified-timeline`, params),\n    getUnifiedTimelineByUuid: (uuid, params)=>apiClient.get(`/conversations/uuid/${uuid}/unified-timeline`, params),\n    appendMessage: (id, data)=>apiClient.post(`/conversations/${id}/messages`, data),\n    appendMessageByUuid: (uuid, data)=>apiClient.post(`/conversations/uuid/${uuid}/messages`, data)\n};\n// Image API methods\nconst imageApi = {\n    upload: (file)=>{\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        return (0,_auth__WEBPACK_IMPORTED_MODULE_0__.fetchWithCredentials)(\"/images\", {\n            method: \"POST\",\n            body: formData\n        }).then((response)=>{\n            if (!response.ok) {\n                return response.json().then((errorData)=>{\n                    throw new Error(errorData.error || \"Failed to upload image\");\n                });\n            }\n            return response.json();\n        });\n    }\n};\n// Agent API methods (using existing endpoints)\nconst agentApi = {\n    getAll: (params)=>apiClient.get(\"/agents\", params),\n    getById: (id)=>apiClient.get(`/agents/${id}`),\n    create: (data)=>apiClient.post(\"/agents\", data),\n    update: (id, data)=>apiClient.put(`/agents/${id}`, data),\n    delete: (id)=>apiClient.delete(`/agents/${id}`),\n    generateMessage: (data)=>apiClient.post(\"/agents/runtime/generate-message\", data)\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/api.ts\n");

/***/ }),

/***/ "./src/utils/auth.ts":
/*!***************************!*\
  !*** ./src/utils/auth.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearClientAuthArtifacts: () => (/* binding */ clearClientAuthArtifacts),\n/* harmony export */   debugLogin: () => (/* binding */ debugLogin),\n/* harmony export */   fetchWithCredentials: () => (/* binding */ fetchWithCredentials),\n/* harmony export */   getBackendUrl: () => (/* binding */ getBackendUrl),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   handleUnauthorized: () => (/* binding */ handleUnauthorized),\n/* harmony export */   performLogout: () => (/* binding */ performLogout),\n/* harmony export */   redirectToGoogleAuth: () => (/* binding */ redirectToGoogleAuth)\n/* harmony export */ });\nconst getBackendUrl = ()=>{\n    return \"http://localhost:8000\" || 0;\n};\nconst clearClientAuthArtifacts = ()=>{\n    console.log(\"[clearClientAuthArtifacts] Clearing all auth artifacts\");\n    try {\n        // Clear local cache of user\n        if (typeof localStorage !== \"undefined\") {\n            localStorage.removeItem(\"teno:auth:user\");\n            localStorage.removeItem(\"teno:auth:token\");\n            console.log(\"[clearClientAuthArtifacts] Cleared localStorage tokens\");\n        }\n    } catch  {}\n    try {\n        // Proactively drop any readable client token if it exists\n        if (typeof document !== \"undefined\") {\n            // Expire both potential names just in case\n            document.cookie = \"access_token_client=; Path=/; Max-Age=0; SameSite=Lax\";\n            document.cookie = \"access_token=; Path=/; Max-Age=0; SameSite=Lax\";\n            console.log(\"[clearClientAuthArtifacts] Cleared cookie tokens\");\n        }\n    } catch  {}\n};\nconst handleUnauthorized = ()=>{\n    console.log(\"[handleUnauthorized] Called - clearing auth artifacts and redirecting to login\");\n    // Ensure client artifacts are cleared immediately\n    clearClientAuthArtifacts();\n    try {\n        // Notify any listeners (e.g., UI) that auth state became unauthorized\n        if (false) {}\n    } catch  {}\n    // Best-effort redirect to login preserving next path\n    try {\n        if (false) {}\n    } catch  {}\n};\nconst fetchWithCredentials = async (input, init)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        ...init && init.headers ? init.headers : {}\n    };\n    // Try to get token from multiple sources in order of preference\n    try {\n        if (typeof document !== \"undefined\" && !headers[\"Authorization\"]) {\n            let token;\n            // First try cookie (primary method)\n            const cookieMatch = document.cookie.match(/(?:^|; )access_token_client=([^;]+)/);\n            if (cookieMatch) {\n                token = decodeURIComponent(cookieMatch[1]);\n                console.log(\"[fetchWithCredentials] Token found in cookie:\", token.substring(0, 20) + \"...\");\n            } else {\n                console.log(\"[fetchWithCredentials] No token found in cookie\");\n            }\n            // Fallback to localStorage if cookie not found\n            if (!token) {\n                try {\n                    token = localStorage.getItem(\"teno:auth:token\") || undefined;\n                    if (token) {\n                        console.log(\"[fetchWithCredentials] Token found in localStorage:\", token.substring(0, 20) + \"...\");\n                    } else {\n                        console.log(\"[fetchWithCredentials] No token found in localStorage\");\n                    }\n                } catch (e) {\n                    console.warn(\"Could not access localStorage:\", e);\n                }\n            }\n            if (token) {\n                headers[\"Authorization\"] = `Bearer ${token}`;\n                console.log(\"[fetchWithCredentials] Token added to Authorization header\");\n            } else {\n                console.log(\"[fetchWithCredentials] No token available for Authorization header\");\n            }\n        }\n    } catch (e) {\n        console.warn(\"Error getting auth token:\", e);\n    }\n    const response = await fetch(input, {\n        ...init,\n        credentials: \"include\",\n        headers\n    });\n    // If backend says unauthorized/forbidden, clear local auth and nudge UI\n    if (response.status === 401 || response.status === 403) {\n        handleUnauthorized();\n    }\n    return response;\n};\nconst getCurrentUser = async ()=>{\n    // Check if we have a token before making the API call\n    let hasToken = false;\n    try {\n        if (typeof document !== \"undefined\") {\n            const cookieMatch = document.cookie.match(/(?:^|; )access_token_client=([^;]+)/);\n            if (cookieMatch) {\n                hasToken = true;\n            }\n        }\n        if (!hasToken && typeof localStorage !== \"undefined\") {\n            try {\n                const token = localStorage.getItem(\"teno:auth:token\");\n                if (token) {\n                    hasToken = true;\n                }\n            } catch (e) {\n                console.warn(\"Could not access localStorage:\", e);\n            }\n        }\n    } catch (e) {\n        console.warn(\"Error checking for token:\", e);\n    }\n    if (!hasToken) {\n        console.log(\"[getCurrentUser] No token found, skipping API call\");\n        return null;\n    }\n    const url = `${getBackendUrl()}/auth/me`;\n    console.log(\"[getCurrentUser] Making API call to:\", url);\n    const response = await fetchWithCredentials(url);\n    if (!response.ok) {\n        console.log(\"[getCurrentUser] API call failed with status:\", response.status);\n        return null;\n    }\n    const data = await response.json();\n    console.log(\"[getCurrentUser] API call successful, user:\", data.user);\n    return data.user ?? null;\n};\nconst performLogout = async ()=>{\n    const url = `${getBackendUrl()}/auth/logout`;\n    try {\n        await fetchWithCredentials(url, {\n            method: \"POST\"\n        });\n    } finally{\n        // Always clear client artifacts regardless of server response\n        clearClientAuthArtifacts();\n    }\n};\nconst redirectToGoogleAuth = (nextPath)=>{\n    let url = `${getBackendUrl()}/auth/google`;\n    try {\n        // Prefer explicit nextPath, otherwise pick it up from current URL (?next=...)\n        let nextParam = nextPath;\n        if (!nextParam && \"undefined\" !== \"undefined\") {}\n        if (nextParam) {\n            // Only allow app-internal paths starting with '/'\n            const safeNext = decodeURIComponent(nextParam);\n            if (safeNext.startsWith(\"/\")) {\n                url += `?next=${encodeURIComponent(safeNext)}`;\n            }\n        }\n    } catch  {}\n    if (false) {}\n};\nconst debugLogin = async (email, name)=>{\n    const url = `${getBackendUrl()}/auth/dev-login`;\n    console.log(\"[debugLogin] Attempting debug login for:\", email);\n    const response = await fetch(url, {\n        method: \"POST\",\n        headers: {\n            \"Content-Type\": \"application/json\"\n        },\n        body: JSON.stringify({\n            email,\n            name\n        })\n    });\n    if (!response.ok) {\n        console.error(\"[debugLogin] Login failed with status:\", response.status);\n        return null;\n    }\n    const data = await response.json();\n    console.log(\"[debugLogin] Login successful, received data:\", data);\n    // Extract and store the token\n    if (data.access_token) {\n        console.log(\"[debugLogin] Storing access token\");\n        // Store in cookie\n        const isLocalhost =  false && (0);\n        const cookieOptions = isLocalhost ? `path=/; max-age=86400; samesite=lax` : `path=/; max-age=86400; secure; samesite=strict`;\n        document.cookie = `access_token_client=${encodeURIComponent(data.access_token)}; ${cookieOptions}`;\n        // Store in localStorage as backup\n        try {\n            localStorage.setItem(\"teno:auth:token\", data.access_token);\n        } catch (e) {\n            console.warn(\"[debugLogin] Could not store token in localStorage:\", e);\n        }\n        console.log(\"[debugLogin] Token stored successfully\");\n    } else {\n        console.warn(\"[debugLogin] No access_token in response\");\n    }\n    return data.user ?? null;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/auth.ts\n");

/***/ }),

/***/ "./src/utils/preferences.ts":
/*!**********************************!*\
  !*** ./src/utils/preferences.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CURRENCY_OPTIONS: () => (/* binding */ CURRENCY_OPTIONS),\n/* harmony export */   CURRENCY_SYMBOLS: () => (/* binding */ CURRENCY_SYMBOLS),\n/* harmony export */   DEFAULT_CURRENCY_BY_COUNTRY: () => (/* binding */ DEFAULT_CURRENCY_BY_COUNTRY),\n/* harmony export */   LANGUAGE_OPTIONS: () => (/* binding */ LANGUAGE_OPTIONS),\n/* harmony export */   TRANSLATIONS: () => (/* binding */ TRANSLATIONS),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   getBrowserPreferenceDefaults: () => (/* binding */ getBrowserPreferenceDefaults),\n/* harmony export */   getStoreCurrency: () => (/* binding */ getStoreCurrency),\n/* harmony export */   getTranslation: () => (/* binding */ getTranslation)\n/* harmony export */ });\nconst LANGUAGE_OPTIONS = [\n    \"en\",\n    \"es\",\n    \"fr\",\n    \"de\",\n    \"it\",\n    \"pt\",\n    \"ru\",\n    \"zh\",\n    \"ja\",\n    \"ko\",\n    \"ar\",\n    \"hi\",\n    \"bn\",\n    \"pa\",\n    \"ur\",\n    \"fa\",\n    \"tr\",\n    \"nl\",\n    \"sv\",\n    \"no\",\n    \"da\",\n    \"fi\",\n    \"pl\",\n    \"cs\",\n    \"sk\",\n    \"hu\",\n    \"ro\",\n    \"bg\",\n    \"hr\",\n    \"sl\"\n];\nconst CURRENCY_OPTIONS = [\n    \"USD\",\n    \"EUR\",\n    \"DZD\",\n    \"GBP\",\n    \"CAD\",\n    \"AUD\",\n    \"NZD\",\n    \"JPY\",\n    \"CNY\",\n    \"HKD\",\n    \"SGD\",\n    \"INR\",\n    \"BRL\",\n    \"MXN\",\n    \"ZAR\",\n    \"SEK\",\n    \"NOK\",\n    \"DKK\",\n    \"CHF\",\n    \"PLN\",\n    \"CZK\",\n    \"HUF\",\n    \"ILS\",\n    \"TRY\",\n    \"AED\",\n    \"SAR\",\n    \"QAR\",\n    \"KWD\",\n    \"BHD\",\n    \"OMR\",\n    \"EGP\",\n    \"NGN\",\n    \"KES\",\n    \"ARS\",\n    \"CLP\",\n    \"COP\",\n    \"PEN\",\n    \"UYU\",\n    \"KRW\",\n    \"THB\",\n    \"MYR\",\n    \"PHP\",\n    \"IDR\"\n];\nconst CURRENCY_SYMBOLS = {\n    \"USD\": \"$\",\n    \"EUR\": \"€\",\n    \"DZD\": \"DZD\",\n    \"GBP\": \"\\xa3\",\n    \"CAD\": \"C$\",\n    \"AUD\": \"A$\",\n    \"NZD\": \"NZ$\",\n    \"JPY\": \"\\xa5\",\n    \"CNY\": \"\\xa5\",\n    \"HKD\": \"HK$\",\n    \"SGD\": \"S$\",\n    \"INR\": \"₹\",\n    \"BRL\": \"R$\",\n    \"MXN\": \"$\",\n    \"ZAR\": \"R\",\n    \"SEK\": \"kr\",\n    \"NOK\": \"kr\",\n    \"DKK\": \"kr\",\n    \"CHF\": \"CHF\",\n    \"PLN\": \"PLN\",\n    \"CZK\": \"CZK\",\n    \"HUF\": \"HUF\",\n    \"ILS\": \"₪\",\n    \"TRY\": \"₺\",\n    \"AED\": \"AED\",\n    \"SAR\": \"SAR\",\n    \"QAR\": \"QAR\",\n    \"KWD\": \"KWD\",\n    \"BHD\": \"BHD\",\n    \"OMR\": \"OMR\",\n    \"EGP\": \"EGP\",\n    \"NGN\": \"NGN\",\n    \"KES\": \"KES\",\n    \"ARS\": \"ARS\",\n    \"CLP\": \"CLP\",\n    \"COP\": \"COP\",\n    \"PEN\": \"PEN\",\n    \"UYU\": \"UYU\",\n    \"KRW\": \"₩\",\n    \"THB\": \"฿\",\n    \"MYR\": \"RM\",\n    \"PHP\": \"₱\",\n    \"IDR\": \"IDR\"\n};\nconst DEFAULT_CURRENCY_BY_COUNTRY = {\n    US: \"USD\",\n    GB: \"GBP\",\n    CA: \"CAD\",\n    AU: \"AUD\",\n    NZ: \"NZD\",\n    JP: \"JPY\",\n    CN: \"CNY\",\n    HK: \"HKD\",\n    SG: \"SGD\",\n    IN: \"INR\",\n    BR: \"BRL\",\n    MX: \"MXN\",\n    ZA: \"ZAR\",\n    SE: \"SEK\",\n    NO: \"NOK\",\n    DK: \"DKK\",\n    CH: \"CHF\",\n    PL: \"PLN\",\n    CZ: \"CZK\",\n    HU: \"HUF\",\n    IL: \"ILS\",\n    TR: \"TRY\",\n    AE: \"AED\",\n    SA: \"SAR\",\n    DZ: \"DZD\",\n    DE: \"EUR\",\n    FR: \"EUR\",\n    ES: \"EUR\",\n    IT: \"EUR\",\n    PT: \"EUR\"\n};\nfunction getBrowserPreferenceDefaults() {\n    let language = \"en\";\n    let country = \"US\";\n    try {\n        if (typeof navigator !== \"undefined\") {\n            const browserLang = navigator.language || \"en\";\n            // Convert browser language to supported format (e.g., 'en-US' -> 'en')\n            language = browserLang.split(\"-\")[0];\n            if (browserLang.split(\"-\").length > 1) country = browserLang.split(\"-\")[1].toUpperCase();\n        }\n    } catch  {}\n    const currency = DEFAULT_CURRENCY_BY_COUNTRY[country] || \"USD\";\n    return {\n        language,\n        country,\n        currency\n    };\n}\nfunction formatCurrency(amount, currency = \"USD\") {\n    const symbol = CURRENCY_SYMBOLS[currency] || currency;\n    return `${symbol}${amount.toLocaleString(\"en-US\", {\n        minimumFractionDigits: 2,\n        maximumFractionDigits: 2\n    })}`;\n}\n// Simple translation system for supported languages\nconst TRANSLATIONS = {\n    \"en\": {\n        \"storeDirectory\": \"Store Directory\",\n        \"browseStores\": \"Browse available stores and their products\",\n        \"visitStore\": \"Visit Store\",\n        \"shoppingCart\": \"Shopping Cart\",\n        \"addToCart\": \"Add to Cart\",\n        \"proceedToCheckout\": \"Proceed to Checkout\",\n        \"loading\": \"Loading...\",\n        \"noProducts\": \"No products available in this store.\",\n        \"howItWorks\": \"How it works\",\n        \"browseAndShop\": \"Browse stores, view their products, and add items to your cart. No account required - start shopping immediately!\"\n    },\n    \"fr\": {\n        \"storeDirectory\": \"R\\xe9pertoire des Magasins\",\n        \"browseStores\": \"Parcourez les magasins disponibles et leurs produits\",\n        \"visitStore\": \"Visiter le Magasin\",\n        \"shoppingCart\": \"Panier d'Achats\",\n        \"addToCart\": \"Ajouter au Panier\",\n        \"proceedToCheckout\": \"Proc\\xe9der au Paiement\",\n        \"loading\": \"Chargement...\",\n        \"noProducts\": \"Aucun produit disponible dans ce magasin.\",\n        \"howItWorks\": \"Comment \\xe7a marche\",\n        \"browseAndShop\": \"Parcourez les magasins, consultez leurs produits et ajoutez des articles \\xe0 votre panier. Aucun compte requis - commencez \\xe0 faire vos achats imm\\xe9diatement !\"\n    },\n    \"ar\": {\n        \"storeDirectory\": \"دليل المتاجر\",\n        \"browseStores\": \"تصفح المتاجر المتاحة ومنتجاتها\",\n        \"visitStore\": \"زيارة المتجر\",\n        \"shoppingCart\": \"سلة التسوق\",\n        \"addToCart\": \"إضافة إلى السلة\",\n        \"proceedToCheckout\": \"المتابعة للدفع\",\n        \"loading\": \"جاري التحميل...\",\n        \"noProducts\": \"لا توجد منتجات متاحة في هذا المتجر.\",\n        \"howItWorks\": \"كيف يعمل\",\n        \"browseAndShop\": \"تصفح المتاجر، عرض منتجاتها، وإضافة العناصر إلى سلة التسوق الخاصة بك. لا حاجة لحساب - ابدأ التسوق فوراً!\"\n    }\n};\nfunction getTranslation(key, language = \"en\") {\n    return TRANSLATIONS[language]?.[key] || TRANSLATIONS[\"en\"][key] || key;\n}\n// Get store currency - currently defaults to USD, but can be easily updated\n// to use store-specific currency when that field is added to the Store model\nfunction getStoreCurrency(store) {\n    // TODO: When store.currency field is added, use: return store?.currency || 'USD';\n    return \"USD\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/preferences.ts\n");

/***/ }),

/***/ "./src/utils/useAuthGuard.ts":
/*!***********************************!*\
  !*** ./src/utils/useAuthGuard.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAuthGuard: () => (/* binding */ useAuthGuard)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context_AuthContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../context/AuthContext */ \"./src/context/AuthContext.tsx\");\n/* harmony import */ var _context_StoreContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../context/StoreContext */ \"./src/context/StoreContext.tsx\");\n\n\n\n\n// Routes that don't require authentication\nconst PUBLIC_ROUTES = [\n    \"/login\",\n    \"/auth/callback\",\n    \"/setup/store\"\n];\nfunction useAuthGuard() {\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const { user, isLoading, error } = (0,_context_AuthContext__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const { currentStoreId, setCurrentStoreId } = (0,_context_StoreContext__WEBPACK_IMPORTED_MODULE_3__.useStore)();\n    const [storesData, setStoresData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isStoresLoading, setIsStoresLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const handledCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({});\n    const isRedirectingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const isPublic = (path)=>PUBLIC_ROUTES.some((route)=>path.startsWith(route));\n    // Fetch stores data for the current user\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (!user) return;\n        const fetchStores = async ()=>{\n            setIsStoresLoading(true);\n            try {\n                // Import the store API dynamically to avoid circular dependencies\n                const { storeApi } = await __webpack_require__.e(/*! import() */ \"src_utils_api_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ../utils/api */ \"./src/utils/api.ts\"));\n                const result = await storeApi.getByUserId(user.id, {\n                    page: 1,\n                    limit: 100\n                });\n                setStoresData(result);\n            } catch (error) {\n                console.error(\"Error fetching stores:\", error);\n                setStoresData({\n                    data: [],\n                    meta: {\n                        total: 0\n                    }\n                });\n            } finally{\n                setIsStoresLoading(false);\n            }\n        };\n        fetchStores();\n    }, [\n        user\n    ]);\n    // Main auth guard logic\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        // Add a small delay to allow auth state to stabilize after token storage\n        const timeoutId = setTimeout(()=>{\n            // Avoid redirecting while we are still determining auth state\n            if (isLoading) return;\n            const currentPath = router.pathname;\n            const asPath = router.asPath;\n            const fullPath = asPath || currentPath;\n            const searchParams = new URLSearchParams( false ? 0 : \"\");\n            const loggedOutFlag = searchParams.get(\"loggedOut\");\n            // Ensure we only handle/log once per path after loading has completed\n            if (handledCache.current[currentPath]) {\n                return;\n            }\n            console.log(\"[AuthGuard] route/useEffect fired\", {\n                pathname: currentPath,\n                asPath: router.asPath,\n                isLoading,\n                hasUser: !!user,\n                error\n            });\n            if (!user && !isPublic(currentPath)) {\n                if (isRedirectingRef.current) {\n                    console.log(\"[AuthGuard] Redirect already in progress; skipping\");\n                    return;\n                }\n                isRedirectingRef.current = true;\n                const nextParam = encodeURIComponent(fullPath || \"/\");\n                const loginUrl = `/login?next=${nextParam}`;\n                console.log(\"[AuthGuard] Not authenticated; redirecting to\", loginUrl);\n                router.replace(loginUrl).finally(()=>{\n                    setTimeout(()=>{\n                        isRedirectingRef.current = false;\n                    }, 0);\n                });\n                handledCache.current[currentPath] = true;\n                return;\n            }\n            if (isPublic(currentPath) && currentPath === \"/login\" && loggedOutFlag === \"1\") {\n                handledCache.current[currentPath] = true;\n                return;\n            }\n            if (user && isPublic(currentPath) && currentPath === \"/login\") {\n                if (isStoresLoading) {\n                    return;\n                }\n                if (storesData === null) {\n                    return;\n                }\n                if (!isRedirectingRef.current) {\n                    isRedirectingRef.current = true;\n                    let target = \"/dashboard\";\n                    try {\n                        const nextParam = searchParams.get(\"next\") || undefined;\n                        const totalActive = storesData?.meta?.total ?? storesData?.data?.length ?? 0;\n                        if (nextParam && nextParam.startsWith(\"/\")) {\n                            target = nextParam;\n                        } else {\n                            if (totalActive === 0) {\n                                target = \"/setup/store\";\n                            }\n                        }\n                    } catch  {}\n                    console.log(\"[AuthGuard] Already authenticated; redirecting away from public auth page to\", target);\n                    router.replace(target).finally(()=>{\n                        setTimeout(()=>{\n                            isRedirectingRef.current = false;\n                        }, 0);\n                    });\n                }\n                handledCache.current[currentPath] = true;\n                return;\n            }\n            if (user && !isPublic(currentPath)) {\n                if (storesData === null) {\n                    console.log(\"[AuthGuard] Stores data not yet fetched, waiting...\");\n                    return;\n                }\n                const totalActive = storesData?.meta?.total ?? storesData?.data?.length ?? 0;\n                console.log(\"[AuthGuard] Store check:\", {\n                    currentPath,\n                    totalActive,\n                    storesData,\n                    isStoresLoading,\n                    loggedOutFlag\n                });\n                if (!isStoresLoading && totalActive === 0 && currentPath !== \"/setup/store\" && loggedOutFlag !== \"1\") {\n                    if (!isRedirectingRef.current) {\n                        isRedirectingRef.current = true;\n                        console.log(\"[AuthGuard] Authenticated user without active stores; redirecting to /setup/store\");\n                        router.replace(\"/setup/store\").finally(()=>{\n                            setTimeout(()=>{\n                                isRedirectingRef.current = false;\n                            }, 0);\n                        });\n                        handledCache.current[currentPath] = true;\n                        return;\n                    }\n                } else if (totalActive > 0 && !currentStoreId) {\n                    const firstId = storesData?.data?.[0]?.id;\n                    if (firstId) setCurrentStoreId(String(firstId));\n                }\n            }\n            if (!(user && !isPublic(currentPath) && isStoresLoading)) {\n                handledCache.current[currentPath] = true;\n            }\n        }, 100); // 100ms delay to allow auth state to stabilize\n        return ()=>clearTimeout(timeoutId);\n    }, [\n        router.pathname,\n        isLoading,\n        user,\n        error,\n        isStoresLoading,\n        storesData,\n        currentStoreId,\n        router,\n        setCurrentStoreId\n    ]);\n    return {\n        user,\n        isLoading,\n        error,\n        storesData,\n        isStoresLoading,\n        currentStoreId\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/utils/useAuthGuard.ts\n");

/***/ }),

/***/ "./src/components/EntityTable.fadein.css":
/*!***********************************************!*\
  !*** ./src/components/EntityTable.fadein.css ***!
  \***********************************************/
/***/ (() => {



/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "next/head":
/*!****************************!*\
  !*** external "next/head" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/head");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Ftrack%2F%5BorderNumber%5D&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Ctrack%5C%5BorderNumber%5D.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();